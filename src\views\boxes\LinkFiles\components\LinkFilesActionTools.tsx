import Button from '@/components/ui/Button'
// import useTranslation from '@/utils/hooks/useTranslation'
import { TbLink } from 'react-icons/tb'

const LinkFilesActionTools = () => {
    // const { t } = useTranslation()

    return (
        <div className="flex items-center gap-2">
            <Button
                disabled
                variant="solid"
                size="sm"
                icon={<TbLink />}
                className="flex items-center gap-2"
                onClick={() => alert('Link files')}
            >
                Link range files
            </Button>
        </div>
    )
}

export default LinkFilesActionTools
