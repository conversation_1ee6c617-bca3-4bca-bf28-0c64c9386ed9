import { useQuery } from '@tanstack/react-query'
import { getTransfers } from '@/services/Warehouses'
import type { TransfersResponse } from '@/@types/warehouse'

interface UseGetTransfersParams {
    page?: number
    pageSize?: number
}

export const useGetTransfers = (params: UseGetTransfersParams = {}) => {
    const { page = 1, pageSize = 10 } = params

    return useQuery<TransfersResponse>({
        queryKey: ['transfers', page, pageSize],
        queryFn: () => getTransfers(page, pageSize),
        staleTime: 5 * 60 * 1000, // 5 minutes
        gcTime: 10 * 60 * 1000, // 10 minutes
        retry: 3,
    })
}
