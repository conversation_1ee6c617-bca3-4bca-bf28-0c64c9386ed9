import React, { useState } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import { But<PERSON>, Card } from '@/components/ui'
import { TbArrowLeft, TbDeviceFloppy, TbInfoCircle } from 'react-icons/tb'
import DocumentMetadata from './components/DocumentMetadata'
import FileDisplay from './components/FileDisplay'
import {
    useGetDocumentById,
    useMarkDocumentAsDigitized,
} from '@/hooks/documents'
import MetadataDrawer from './components/MetadataDrawer'

export default function DocsReview() {
    const { documentId, fileId } = useParams()
    const { data: document, isLoading } = useGetDocumentById(documentId!)
    const changeStatus = useMarkDocumentAsDigitized()

    const navigate = useNavigate()
    const { t } = useTranslation()

    const [isMetadataDrawerOpen, setIsMetadataDrawerOpen] = useState(false)

    const handleClose = () => {
        setIsMetadataDrawerOpen(false)
        changeStatus.mutate({ documentId: documentId! })
    }

    const handleBack = () => {
        navigate(`/digitization/${fileId}/documents`)
    }

    return (
        <Card className="">
            {/* Header */}
            <div className=" border-b border-gray-200 px-6 py-4">
                <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                        <Button
                            variant="plain"
                            size="sm"
                            className="flex items-center gap-2"
                            onClick={handleBack}
                        >
                            <TbArrowLeft />
                            {t('nav.shared.back')}
                        </Button>

                        <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-3">
                                <Button
                                    variant="default"
                                    size="xs"
                                    className="flex items-center space-x-2"
                                    onClick={() =>
                                        setIsMetadataDrawerOpen(true)
                                    }
                                >
                                    <TbInfoCircle className="w-4 h-4" />
                                </Button>
                                <div>
                                    <h2 className="text-xl font-semibold text-gray-900">
                                        {document?.title || 'Document'}
                                    </h2>
                                    <p className="text-sm text-gray-500">
                                        Code: {document?.documentId || '-'}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    {document?.status === 0 && (
                        <div className="flex items-center gap-3">
                            <Button
                                variant="solid"
                                size="sm"
                                className="flex items-center gap-2"
                                onClick={handleClose}
                            >
                                <TbDeviceFloppy />
                                تمت الرقمنه
                            </Button>
                        </div>
                    )}
                </div>
            </div>

            {/* Main Content */}
            <div className="max-w-7xl mx-auto px-6 py-8">
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    {/* Left Column - Metadata and Upload */}
                    <div className="lg:col-span-1 space-y-6">
                        {/* Document Metadata */}
                        <DocumentMetadata
                            document={document!}
                            loading={isLoading}
                        />
                    </div>

                    {/* Right Column - File Display */}
                    <div className="lg:col-span-2">
                        <FileDisplay
                            documentId={documentId}
                            documentUrl={document?.url}
                            documentTitle={document?.title}
                            documentType="pdf"
                        />
                    </div>
                </div>

                {/* Attachments Section */}
                {document?.attachments && document.attachments.length > 0 && (
                    <div className="mt-8">
                        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                            <h3 className="text-lg font-semibold text-gray-900 mb-4">
                                {t('documents.attachments')}
                            </h3>
                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                {document.attachments.map((attachment) => (
                                    <div
                                        key={attachment.id}
                                        className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
                                    >
                                        <div className="flex items-center justify-between">
                                            <div>
                                                <p className="font-medium text-gray-900">
                                                    Attachment {attachment.id}
                                                </p>
                                                <p className="text-sm text-gray-500">
                                                    {new Date(
                                                        attachment.createdAt,
                                                    ).toLocaleDateString()}
                                                </p>
                                            </div>
                                            <Button
                                                variant="default"
                                                size="sm"
                                                onClick={() =>
                                                    window.open(
                                                        attachment.url,
                                                        '_blank',
                                                    )
                                                }
                                            >
                                                {t('nav.shared.view')}
                                            </Button>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>
                    </div>
                )}
            </div>

            {/* Metadata Drawer */}
            <MetadataDrawer
                document={document!}
                isOpen={isMetadataDrawerOpen}
                onClose={() => setIsMetadataDrawerOpen(false)}
            />
        </Card>
    )
}
