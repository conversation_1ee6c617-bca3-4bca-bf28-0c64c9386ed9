import Select from '@/components/ui/Select'
import useTranslation from '@/utils/hooks/useTranslation'
import {
    getFileStatusOptions,
    getFileStatusOptionsForExport,
    getFileStatusOptionsForLinking,
} from '@/utils/status'

interface FileStatusSelectProps {
    value: string
    onChange: (value: string) => void
    placeholder?: string
    includeAll?: boolean
    variant?: 'default' | 'linking' | 'export'
    className?: string
    isDisabled?: boolean
    menuPlacement?: 'auto' | 'bottom' | 'top'
}

const FileStatusSelect = ({
    value,
    onChange,
    placeholder,
    includeAll = false,
    variant = 'default',
    className = '',
    isDisabled = false,
    menuPlacement = 'auto',
}: FileStatusSelectProps) => {
    const { t } = useTranslation()

    // Get status options based on variant
    const getStatusOptions = () => {
        switch (variant) {
            case 'linking':
                return getFileStatusOptionsForLinking(t)
            case 'export':
                return getFileStatusOptionsForExport(t)
            case 'default':
            default:
                return getFileStatusOptions(t, includeAll)
        }
    }

    const statusOptions = getStatusOptions()

    // Find the selected option
    const selectedOption = statusOptions.find(
        (option) => option.value === value,
    )

    const handleChange = (
        selectedOption: { value: string; label: string } | null,
    ) => {
        onChange(selectedOption?.value || '')
    }

    return (
        <Select
            value={selectedOption}
            options={statusOptions}
            placeholder={placeholder || t('nav.shared.selectStatus')}
            className={className}
            isDisabled={isDisabled}
            menuPlacement={menuPlacement}
            onChange={handleChange}
        />
    )
}

export default FileStatusSelect
