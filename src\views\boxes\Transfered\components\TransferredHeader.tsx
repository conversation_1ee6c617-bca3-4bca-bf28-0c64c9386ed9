import Button from '@/components/ui/Button'
import useTranslation from '@/utils/hooks/useTranslation'
import { TbRefresh, TbDownload } from 'react-icons/tb'

const TransferredHeader = () => {
    const { t } = useTranslation()

    const handleExport = () => {
        // TODO: Implement export functionality
        console.log('Export transfers data')
    }

    return (
        <div className="flex items-center gap-2">
            <Button
                variant="default"
                size="sm"
                icon={<TbDownload />}
                onClick={handleExport}
            >
                {t('nav.shared.export')}
            </Button>
        </div>
    )
}

export default TransferredHeader
