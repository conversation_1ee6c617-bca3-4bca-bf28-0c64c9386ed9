import { useMemo } from 'react'
import useTranslation from '@/utils/hooks/useTranslation'
import { BoxStatusConfig } from '@/@types/status'
import { getBoxStatusByValue } from '@/utils/status'

interface BoxStatusProps {
    status: number
    variant?: 'badge' | 'text' | 'full'
    showIcon?: boolean
    className?: string
}

const BoxStatus = ({
    status,
    variant = 'badge',
    showIcon = false,
    className = '',
}: BoxStatusProps) => {
    const { t } = useTranslation()

    const statusConfig: BoxStatusConfig | null = useMemo(() => {
        return getBoxStatusByValue(status, t)
    }, [status, t])

    if (!statusConfig) {
        return (
            <span className={`text-gray-500 ${className}`}>
                {t('nav.shared.unknown')}
            </span>
        )
    }

    const renderBadge = () => (
        <span
            className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${statusConfig.className} ${className}`}
        >
            {showIcon && (
                <span className="mr-1">
                    {/* Icon would be rendered here if needed */}
                </span>
            )}
            {statusConfig.label}
        </span>
    )

    const renderText = () => (
        <span className={`text-sm font-medium ${className}`}>
            {statusConfig.label}
        </span>
    )

    const renderFull = () => (
        <div className={`flex flex-col ${className}`}>
            <span
                className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${statusConfig.className} mb-1`}
            >
                {statusConfig.label}
            </span>
            <span className="text-xs text-gray-500">
                {statusConfig.description}
            </span>
        </div>
    )

    switch (variant) {
        case 'text':
            return renderText()
        case 'full':
            return renderFull()
        case 'badge':
        default:
            return renderBadge()
    }
}

export default BoxStatus
