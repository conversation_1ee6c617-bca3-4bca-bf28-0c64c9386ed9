import { useMemo } from 'react'
import Dialog from '@/components/ui/Dialog'
import DataTable from '@/components/shared/DataTable'
import type { ColumnDef } from '@/components/shared/DataTable'
import useTranslation from '@/utils/hooks/useTranslation'
import { useGetTransferById } from '@/hooks/warehouses'
import { TransferBox } from '@/@types/warehouse'
import {
    TbBuilding,
    TbCalendar,
    TbUser,
    TbNotes,
    TbPackages,
    TbFiles,
    TbFileText,
} from 'react-icons/tb'

interface TransferDetailsModalProps {
    isOpen: boolean
    transferId: string | null
    onClose: () => void
}

const TransferDetailsModal = ({
    isOpen,
    transferId,
    onClose,
}: TransferDetailsModalProps) => {
    const { t } = useTranslation()
    const { data: transferDetails, isLoading } = useGetTransferById(
        transferId || undefined,
    )

    const boxColumns = useMemo<ColumnDef<TransferBox>[]>(
        () => [
            {
                header: t('nav.transferBoxes.boxId'),
                accessorKey: 'boxId',
                cell: ({ row }) => (
                    <div className="font-medium text-primary">
                        {row.original.boxId}
                    </div>
                ),
            },
            {
                header: t('nav.transferBoxes.filesCount'),
                accessorKey: 'filesCount',
                cell: ({ row }) => (
                    <div className="flex items-center gap-2">
                        <TbFiles className="w-4 h-4 text-gray-500" />
                        <span>{row.original.filesCount}</span>
                    </div>
                ),
            },
            {
                header: t('nav.transferBoxes.pagesCount'),
                accessorKey: 'pagesCount',
                cell: ({ row }) => (
                    <div className="flex items-center gap-2">
                        <TbFileText className="w-4 h-4 text-gray-500" />
                        <span>{row.original.pagesCount}</span>
                    </div>
                ),
            },
            {
                header: t('nav.transferBoxes.currentLocation'),
                accessorKey: 'currentLocation',
                cell: ({ row }) => (
                    <div className="text-sm">
                        <div>
                            {row.original.currentLocation.locationDescription}
                        </div>
                        <div className="text-gray-500 text-xs">
                            {t('nav.transferBoxes.placedAt')}:{' '}
                            {new Date(
                                row.original.currentLocation.placedAt,
                            ).toLocaleDateString()}
                        </div>
                    </div>
                ),
            },
        ],
        [t],
    )

    if (!transferDetails && !isLoading) {
        return null
    }

    return (
        <Dialog isOpen={isOpen} width={1000} onClose={onClose}>
            <div className="flex flex-col h-full">
                {/* Header */}
                <div className="flex items-center justify-between pb-4 border-b border-gray-200">
                    <h2 className="text-xl font-semibold text-gray-900">
                        {t('nav.transferBoxes.transferDetails')}
                    </h2>
                </div>

                {/* Content */}
                <div className="flex-1 p-6 overflow-y-auto">
                    {isLoading ? (
                        <div className="flex items-center justify-center h-32">
                            <div className="text-gray-500">
                                {t('nav.shared.loading')}
                            </div>
                        </div>
                    ) : transferDetails ? (
                        <div className="space-y-6">
                            {/* Transfer Information */}
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div className="space-y-4">
                                    <div className="flex items-center gap-3">
                                        <TbBuilding className="w-5 h-5 text-gray-500" />
                                        <div>
                                            <div className="text-sm text-gray-500">
                                                {t(
                                                    'nav.transferBoxes.warehouse',
                                                )}
                                            </div>
                                            <div className="font-medium">
                                                {transferDetails.warehouse.name}
                                                :{' '}
                                                {
                                                    transferDetails.warehouse
                                                        .warehouseId
                                                }
                                            </div>
                                        </div>
                                    </div>

                                    <div className="flex items-center gap-3">
                                        <TbUser className="w-5 h-5 text-gray-500" />
                                        <div>
                                            <div className="text-sm text-gray-500">
                                                {t(
                                                    'nav.transferBoxes.createdBy',
                                                )}
                                            </div>
                                            <div className="font-medium">
                                                {transferDetails.createdBy}
                                            </div>
                                        </div>
                                    </div>

                                    <div className="flex items-center gap-3">
                                        <TbCalendar className="w-5 h-5 text-gray-500" />
                                        <div>
                                            <div className="text-sm text-gray-500">
                                                {t(
                                                    'nav.transferBoxes.createdAt',
                                                )}
                                            </div>
                                            <div className="font-medium">
                                                {new Date(
                                                    transferDetails.createdAt,
                                                ).toLocaleString()}
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div className="space-y-4">
                                    <div className="flex items-center gap-3">
                                        <TbPackages className="w-5 h-5 text-gray-500" />
                                        <div>
                                            <div className="text-sm text-gray-500">
                                                {t(
                                                    'nav.transferBoxes.totalBoxes',
                                                )}
                                            </div>
                                            <div className="font-medium">
                                                {
                                                    transferDetails.totalBoxesCount
                                                }
                                            </div>
                                        </div>
                                    </div>

                                    <div className="flex items-center gap-3">
                                        <TbFiles className="w-5 h-5 text-gray-500" />
                                        <div>
                                            <div className="text-sm text-gray-500">
                                                {t(
                                                    'nav.transferBoxes.totalFiles',
                                                )}
                                            </div>
                                            <div className="font-medium">
                                                {
                                                    transferDetails.totalFilesCount
                                                }
                                            </div>
                                        </div>
                                    </div>

                                    <div className="flex items-center gap-3">
                                        <TbFileText className="w-5 h-5 text-gray-500" />
                                        <div>
                                            <div className="text-sm text-gray-500">
                                                {t(
                                                    'nav.transferBoxes.totalPages',
                                                )}
                                            </div>
                                            <div className="font-medium">
                                                {
                                                    transferDetails.totalPagesCount
                                                }
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            {/* Notes */}
                            {transferDetails.notes && (
                                <div className="space-y-2">
                                    <div className="flex items-center gap-2">
                                        <TbNotes className="w-5 h-5 text-gray-500" />
                                        <span className="text-sm text-gray-500">
                                            {t('nav.transferBoxes.notes')}
                                        </span>
                                    </div>
                                    <div className="p-3 bg-gray-50 rounded-lg">
                                        <p className="text-gray-900">
                                            {transferDetails.notes}
                                        </p>
                                    </div>
                                </div>
                            )}

                            {/* Boxes Table */}
                            <div className="space-y-4">
                                <h3 className="text-lg font-medium text-gray-900">
                                    {t('nav.transferBoxes.transferredBoxes')}
                                </h3>
                                <DataTable
                                    columns={boxColumns}
                                    data={transferDetails.boxes}
                                    loading={false}
                                    disablePageSize={true}
                                />
                            </div>
                        </div>
                    ) : null}
                </div>
            </div>
        </Dialog>
    )
}

export default TransferDetailsModal
