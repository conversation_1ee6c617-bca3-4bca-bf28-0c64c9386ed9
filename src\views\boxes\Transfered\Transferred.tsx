import { useState } from 'react'
import Container from '@/components/shared/Container'
import AdaptiveCard from '@/components/shared/AdaptiveCard'
import TransferredHeader from './components/TransferredHeader'
import TransferredTableTools from './components/TransferredTableTools'
import TransferredTable from './components/TransferredTable'
import TransferDetailsModal from './components/TransferDetailsModal'
import useTranslation from '@/utils/hooks/useTranslation'
import { TbTruck } from 'react-icons/tb'
import { useGetTransfers } from '@/hooks/warehouses'

const Transferred = () => {
    const { t } = useTranslation()
    const [currentPage, setCurrentPage] = useState(1)
    const [pageSize, setPageSize] = useState(10)
    const [selectedTransferId, setSelectedTransferId] = useState<string | null>(
        null,
    )
    const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false)

    const { data: transfersData, isLoading } = useGetTransfers({
        page: currentPage,
        pageSize: pageSize,
    })

    const handleViewDetails = (transferId: string) => {
        setSelectedTransferId(transferId)
        setIsDetailsModalOpen(true)
    }

    const handleCloseDetailsModal = () => {
        setIsDetailsModalOpen(false)
        setSelectedTransferId(null)
    }

    const handlePageChange = (page: number) => {
        setCurrentPage(page)
    }

    const handlePageSizeChange = (size: number) => {
        setPageSize(size)
        setCurrentPage(1) // Reset to first page when changing page size
    }

    return (
        <>
            <Container>
                <AdaptiveCard className="shadow-lg">
                    <div className="flex flex-col gap-6">
                        {/* Enhanced Header Section */}
                        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 pb-4 border-b border-gray-200">
                            <div className="flex justify-between items-center gap-3 w-full">
                                <div className="flex items-center gap-3">
                                    <div className="flex items-center justify-center w-10 h-10 bg-primary-subtle rounded-lg">
                                        <TbTruck className="w-5 h-5 text-primary-deep" />
                                    </div>
                                    <h3 className="">
                                        {t(
                                            'nav.transferBoxes.transferredBoxes',
                                        )}
                                    </h3>
                                </div>
                            </div>
                            <TransferredHeader />
                        </div>

                        {/* Table Tools Section */}
                        <div className="">
                            <TransferredTableTools />
                        </div>

                        {/* Table Section */}
                        <div className="">
                            <TransferredTable
                                transfers={transfersData?.items || []}
                                isLoading={isLoading}
                                currentPage={currentPage}
                                pageSize={pageSize}
                                totalCount={transfersData?.totalCount || 0}
                                onPageChange={handlePageChange}
                                onPageSizeChange={handlePageSizeChange}
                                onViewDetails={handleViewDetails}
                            />
                        </div>
                    </div>
                </AdaptiveCard>
            </Container>

            {/* Transfer Details Modal */}
            <TransferDetailsModal
                isOpen={isDetailsModalOpen}
                transferId={selectedTransferId}
                onClose={handleCloseDetailsModal}
            />
        </>
    )
}

export default Transferred
