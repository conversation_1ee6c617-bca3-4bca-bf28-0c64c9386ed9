import { useMemo } from 'react'
import DataTable from '@/components/shared/DataTable'
import type { ColumnDef } from '@/components/shared/DataTable'
import Button from '@/components/ui/Button'
import useTranslation from '@/utils/hooks/useTranslation'
import { TransferOperation } from '@/@types/warehouse'
import {
    TbEye,
    TbCalendar,
    TbBuilding,
    TbUser,
    TbPackages,
} from 'react-icons/tb'

interface TransferredTableProps {
    transfers: TransferOperation[]
    isLoading: boolean
    currentPage: number
    pageSize: number
    totalCount: number
    onPageChange: (page: number) => void
    onPageSizeChange: (size: number) => void
    onViewDetails: (transferId: string) => void
}

const TransferredTable = ({
    transfers,
    isLoading,
    currentPage,
    pageSize,
    totalCount,
    onPageChange,
    onPageSizeChange,
    onViewDetails,
}: TransferredTableProps) => {
    const { t } = useTranslation()

    const columns = useMemo<ColumnDef<TransferOperation>[]>(
        () => [
            {
                header: t('nav.transferBoxes.transferId'),
                accessorKey: 'transferId',
                cell: ({ row }) => (
                    <div className="font-medium text-primary">
                        {row.original.transferId}
                    </div>
                ),
                enableSorting: false,
            },
            {
                header: t('nav.transferBoxes.warehouse'),
                accessorKey: 'warehouseName',
                cell: ({ row }) => (
                    <div className="flex items-center gap-2">
                        <TbBuilding className="w-4 h-4 text-gray-500" />
                        <span>{row.original.warehouseName}</span>
                    </div>
                ),
                enableSorting: false,
            },
            {
                header: t('nav.transferBoxes.boxesCount'),
                accessorKey: 'boxesCount',
                cell: ({ row }) => (
                    <div className="flex items-center gap-2">
                        <TbPackages className="w-4 h-4 text-gray-500" />
                        <span className="font-medium">
                            {row.original.boxesCount}
                        </span>
                    </div>
                ),
            },
            {
                header: t('nav.transferBoxes.createdBy'),
                accessorKey: 'createByFullName',
                cell: ({ row }) => (
                    <div className="flex items-center gap-2">
                        <TbUser className="w-4 h-4 text-gray-500" />
                        <span>{row.original.createByFullName}</span>
                    </div>
                ),
                enableSorting: false,
            },
            {
                header: t('nav.transferBoxes.createdAt'),
                accessorKey: 'createdAt',
                cell: ({ row }) => (
                    <div className="flex items-center gap-2">
                        <TbCalendar className="w-4 h-4 text-gray-500" />
                        <span>
                            {new Date(
                                row.original.createdAt,
                            ).toLocaleDateString()}
                        </span>
                    </div>
                ),
            },
            {
                header: t('nav.shared.actions'),
                id: 'actions',
                cell: ({ row }) => (
                    <div className="flex items-center gap-2">
                        <Button
                            variant="default"
                            size="xs"
                            icon={<TbEye />}
                            onClick={() =>
                                onViewDetails(row.original.transferId)
                            }
                        >
                            {t('nav.shared.viewDetails')}
                        </Button>
                    </div>
                ),
            },
        ],
        [t, onViewDetails],
    )

    return (
        <DataTable
            columns={columns}
            data={transfers}
            loading={isLoading}
            pagination={{
                pageIndex: currentPage - 1,
                pageSize: pageSize,
                total: totalCount,
                onChange: ({ pageIndex, pageSize: newPageSize }) => {
                    if (newPageSize !== pageSize) {
                        onPageSizeChange(newPageSize)
                    } else {
                        onPageChange(pageIndex + 1)
                    }
                },
            }}
        />
    )
}

export default TransferredTable
