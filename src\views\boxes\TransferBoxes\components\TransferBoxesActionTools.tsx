import Button from '@/components/ui/Button'
import useTranslation from '@/utils/hooks/useTranslation'
import { TbTruck } from 'react-icons/tb'

const TransferBoxesActionTools = () => {
    const { t } = useTranslation()

    return (
        <div className="flex items-center gap-2">
            <Button
                disabled
                variant="solid"
                size="sm"
                icon={<TbTruck />}
                className="flex items-center gap-2"
                onClick={() => alert('Transfer range boxes')}
            >
                {t('nav.transferBoxes.transferRangeBoxes')}
            </Button>
        </div>
    )
}

export default TransferBoxesActionTools
