import { useState } from 'react'
import Input from '@/components/ui/Input'
import useTranslation from '@/utils/hooks/useTranslation'
import { TbSearch } from 'react-icons/tb'

const TransferredTableTools = () => {
    const { t } = useTranslation()
    const [searchTerm, setSearchTerm] = useState('')

    const handleSearch = (value: string) => {
        setSearchTerm(value)
        // TODO: Implement search functionality
    }

    return (
        <div className="space-y-4">
            {/* Search and Filters Row */}
            <div className="flex flex-wrap items-center gap-3">
                {/* Search Input */}
                <div className="flex-1 min-w-[200px]">
                    <Input
                        placeholder={t('nav.transferBoxes.searchTransfers')}
                        value={searchTerm}
                        prefix={<TbSearch className="text-lg" />}
                        onChange={(e) => handleSearch(e.target.value)}
                    />
                </div>
            </div>
        </div>
    )
}

export default TransferredTableTools
