import { Confidentiality, DigitizationType, MediumType } from '@/@types/file'
import { FileStatus, FileStatusConfig } from '@/@types/status'

// Comprehensive file status configuration for both dropdowns and tables
export const getFileStatusConfig = (
    t: (key: string) => string,
): Record<FileStatus, FileStatusConfig> => ({
    [FileStatus.Open]: {
        value: 0,
        label: t('nav.files.status.open'),
        description: 'File is open and being processed',
        className: 'bg-blue-100 text-blue-800 border-blue-200',
        icon: 'TbFolderOpen',
        color: 'blue',
    },
    [FileStatus.ClosedAndReadyToLinkedToBox]: {
        value: 1,
        label: t('nav.files.status.closedAndReadyToLinkedToBox'),
        description: 'File is closed and ready to be linked to a box',
        className: 'bg-yellow-100 text-yellow-800 border-yellow-200',
        icon: 'TbFolderCheck',
        color: 'yellow',
    },
    [FileStatus.ClosedAndLinkedToBox]: {
        value: 2,
        label: t('nav.files.status.closedAndLinkedToBox'),
        description: 'File is closed and linked to a box',
        className: 'bg-green-100 text-green-800 border-green-200',
        icon: 'TbFolderPlus',
        color: 'green',
    },
    [FileStatus.ClosedAndExported]: {
        value: 3,
        label: t('nav.files.status.closedAndExported'),
        description: 'File is closed and exported',
        className: 'bg-purple-100 text-purple-800 border-purple-200',
        icon: 'TbFolderShare',
        color: 'purple',
    },
})

// Helper function to get status options for dropdowns
export const getFileStatusOptions = (
    t: (key: string) => string,
    includeAll: boolean = false,
) => {
    const statusConfig = getFileStatusConfig(t)
    const options = Object.values(statusConfig).map((config) => ({
        value: config.value.toString(),
        label: config.label,
    }))

    if (includeAll) {
        return [{ value: '', label: t('nav.shared.allStatuses') }, ...options]
    }

    return options
}

// Helper function to get status configuration by value
export const getFileStatusByValue = (
    status: number,
    t: (key: string) => string,
): FileStatusConfig | null => {
    const statusConfig = getFileStatusConfig(t)
    return statusConfig[status as FileStatus] || null
}

// Helper function to get status options for specific use cases
export const getFileStatusOptionsForLinking = (t: (key: string) => string) => {
    // Only show statuses that are relevant for linking to boxes
    return [
        {
            value: FileStatus.ClosedAndReadyToLinkedToBox.toString(),
            label: t('nav.files.status.closedAndReadyToLinkedToBox'),
        },
    ]
}

export const getFileStatusOptionsForExport = (t: (key: string) => string) => {
    // Only show statuses that are relevant for export
    return [
        {
            value: FileStatus.ClosedAndLinkedToBox.toString(),
            label: t('nav.files.status.closedAndLinkedToBox'),
        },
    ]
}

// Confidentiality Level configurations
export const getConfidentialityConfig = (t: (key: string) => string) => ({
    [Confidentiality.NonConfidential]: {
        value: 0,
        label: t('nav.shared.nonConfidential'),
        description:
            'Non-confidential files accessible to all authorized users',
        className: 'bg-blue-100 text-blue-800 border-blue-200',
        icon: 'TbEye',
        color: 'blue',
    },
    [Confidentiality.Confidential]: {
        value: 1,
        label: t('nav.shared.confidential'),
        description: 'Confidential files with restricted access',
        className: 'bg-yellow-100 text-yellow-800 border-yellow-200',
        icon: 'TbEyeOff',
        color: 'yellow',
    },
    [Confidentiality.HighlyConfidential]: {
        value: 2,
        label: t('nav.shared.highlyConfidential'),
        description: 'Highly confidential files with very restricted access',
        className: 'bg-red-100 text-red-800 border-red-200',
        icon: 'TbShieldLock',
        color: 'red',
    },
})

// Digitization Type configurations
export const getDigitizationTypeConfig = (t: (key: string) => string) => ({
    [DigitizationType.Daily]: {
        value: 2,
        label: t('nav.shared.daily'),
        description: 'Daily digitization files processed on regular schedule',
        className: 'bg-purple-100 text-purple-800 border-purple-200',
        icon: 'TbCalendarEvent',
        color: 'purple',
    },
    [DigitizationType.Backlog]: {
        value: 4,
        label: t('nav.shared.backlog'),
        description: 'Backlog digitization files from previous periods',
        className: 'bg-orange-100 text-orange-800 border-orange-200',
        icon: 'TbArchive',
        color: 'orange',
    },
})

// Medium Type configurations
export const getMediumTypeConfig = (t: (key: string) => string) => ({
    [MediumType.PaperBased]: {
        value: 0,
        label: t('nav.shared.paperBased'),
        description: 'Physical paper documents requiring digitization',
        className: 'bg-amber-100 text-amber-800 border-amber-200',
        icon: 'TbFileText',
        color: 'amber',
    },
    [MediumType.ElectronicWithoutPhysicalCopy]: {
        value: 1,
        label: t('nav.shared.electronicOnly'),
        description: 'Born digital without physical copy',
        className: 'bg-cyan-100 text-cyan-800 border-cyan-200',
        icon: 'TbDeviceDesktop',
        color: 'cyan',
    },
    [MediumType.ElectronicWithPhysicalCopy]: {
        value: 2,
        label: t('nav.shared.electronicWithPhysical'),
        description: 'Born digital with accompanying physical copy',
        className: 'bg-indigo-100 text-indigo-800 border-indigo-200',
        icon: 'TbFiles',
        color: 'indigo',
    },
})
