import {
    BOXES_PREFIX_PATH,
    WAREHOUSES_PREFIX_PATH,
} from '@/constants/route.constant'
import {
    NAV_ITEM_TYPE_ITEM,
    NAV_ITEM_TYPE_COLLAPSE,
} from '@/constants/navigation.constant'
import { ADMIN, USER } from '@/constants/roles.constant'
import type { NavigationTree } from '@/@types/navigation'

const warehouseNavigationConfig: NavigationTree[] = [
    {
        key: 'managements',
        path: '',
        title: 'Management',
        translateKey: 'nav.management.title',
        icon: 'warehouses',
        type: NAV_ITEM_TYPE_COLLAPSE,
        authority: [ADMIN, USER],
        meta: {
            horizontalMenu: {
                layout: 'default',
            },
        },
        subMenu: [
            {
                key: 'managements.boxes',
                path: `${BOXES_PREFIX_PATH}`,
                title: 'Boxes',
                translateKey: 'nav.management.items.boxes',
                icon: '',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [USER],
                subMenu: [],
            },
            {
                key: 'managements.allBoxes',
                path: `${BOXES_PREFIX_PATH}/all`,
                title: 'All Boxes',
                translateKey: 'nav.management.items.boxes',
                icon: '',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [ADMIN],
                subMenu: [],
            },
            {
                key: 'managements.linkFiles',
                path: `${BOXES_PREFIX_PATH}/linkFiles`,
                title: 'Link Files to Boxes',
                translateKey: 'nav.management.items.linkFiles',
                icon: '',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [USER],
                subMenu: [],
            },
            {
                key: 'managements.warehouses',
                path: `${WAREHOUSES_PREFIX_PATH}/warehouses`,
                title: 'Warehouses',
                translateKey: 'nav.management.items.warehouses',
                icon: 'warehouses',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [ADMIN],
                subMenu: [],
            },

            {
                key: 'managements.transferredBoxes',
                path: `${BOXES_PREFIX_PATH}/transferred`,
                title: 'Transferred Boxes',
                translateKey: 'nav.management.items.transferredBoxes',
                icon: '',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [ADMIN],
                subMenu: [],
            },
            {
                key: 'managements.transferBoxes',
                path: `${BOXES_PREFIX_PATH}/transferBoxes`,
                title: 'Transfer Boxes to Warehouse',
                translateKey: 'nav.management.items.transferBoxes',
                icon: '',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [ADMIN],
                subMenu: [],
            },
        ],
    },
]

export default warehouseNavigationConfig
