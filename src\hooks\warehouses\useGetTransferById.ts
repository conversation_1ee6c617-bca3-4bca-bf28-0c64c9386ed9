import { useQuery } from '@tanstack/react-query'
import { getTransferById } from '@/services/Warehouses'
import type { TransferOperationDetails } from '@/@types/warehouse'

export const useGetTransferById = (transferId: string | undefined) => {
    return useQuery<TransferOperationDetails>({
        queryKey: ['transfer', transferId],
        queryFn: () => getTransferById(transferId!),
        enabled: !!transferId,
        staleTime: 5 * 60 * 1000, // 5 minutes
        gcTime: 10 * 60 * 1000, // 10 minutes
        retry: 3,
    })
}
