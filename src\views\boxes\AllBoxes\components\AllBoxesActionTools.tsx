import Button from '@/components/ui/Button'
import useTranslation from '@/utils/hooks/useTranslation'
import { TbDownload } from 'react-icons/tb'

const AllBoxesActionTools = () => {
    const { t } = useTranslation()

    const handleExport = () => {
        // TODO: Implement export functionality
        console.log('Export boxes data')
    }

    return (
        <div className="flex items-center gap-2">
            {/* Export Button */}
            <Button
                variant="default"
                size="sm"
                className="flex items-center gap-2"
                onClick={handleExport}
            >
                <TbDownload className="w-4 h-4" />
                {t('nav.shared.export')}
            </Button>
        </div>
    )
}

export default AllBoxesActionTools
