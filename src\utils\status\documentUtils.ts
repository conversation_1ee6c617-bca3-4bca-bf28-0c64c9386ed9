import { DocumentStatus, DocumentStatusConfig } from '@/@types/status'

export const getDocumentStatusConfig = (
    t: (key: string) => string,
): Record<DocumentStatus, DocumentStatusConfig> => ({
    [DocumentStatus.open]: {
        value: 0,
        label: t('nav.documents.draft'),
        description: 'Document is in draft state and can be edited',
        className: 'bg-yellow-100 text-yellow-800 border-yellow-200',
        icon: 'TbFileText',
        color: 'yellow',
    },
    [DocumentStatus.close]: {
        value: 1,
        label: t('nav.documents.active'),
        description: 'Document is active and in use',
        className: 'bg-green-100 text-green-800 border-green-200',
        icon: 'TbFileCheck',
        color: 'green',
    },
})

export const getDocumentStatusOptions = (
    t: (key: string) => string,
    options: {
        includeAll?: boolean
        allowedStatuses?: DocumentStatus[]
        excludeStatuses?: DocumentStatus[]
    } = {},
) => {
    const { includeAll = true, allowedStatuses, excludeStatuses = [] } = options

    const statusConfig = getDocumentStatusConfig(t)
    let statusOptions = Object.values(statusConfig).map((config) => ({
        value: config.value.toString(),
        label: config.label,
    }))

    if (allowedStatuses && allowedStatuses.length > 0) {
        statusOptions = statusOptions.filter((option) =>
            allowedStatuses.includes(parseInt(option.value) as DocumentStatus),
        )
    }

    if (excludeStatuses.length > 0) {
        statusOptions = statusOptions.filter(
            (option) =>
                !excludeStatuses.includes(
                    parseInt(option.value) as DocumentStatus,
                ),
        )
    }

    if (includeAll) {
        return [
            { value: '', label: t('nav.shared.allStatuses') },
            ...statusOptions,
        ]
    }

    return statusOptions
}

export const getDocumentStatusByValue = (
    status: number,
    t: (key: string) => string,
): DocumentStatusConfig | null => {
    const statusConfig = getDocumentStatusConfig(t)
    return statusConfig[status as DocumentStatus] || null
}

export const getDocumentStatusStyle = (status: DocumentStatus) => {
    switch (status) {
        case DocumentStatus.open:
            return {
                color: '#FFA000', // Yellow
                backgroundColor: '#FFF8E1',
                borderRadius: '4px',
                padding: '4px 8px',
                fontWeight: 500,
            }
        case DocumentStatus.close:
            return {
                color: '#4CAF50', // Green
                backgroundColor: '#E8F5E9',
                borderRadius: '4px',
                padding: '4px 8px',
                fontWeight: 500,
            }
        default:
            return {}
    }
}
