export enum FileStatus {
    Open = 0,
    ClosedAndReadyToLinkedToBox = 1,
    ClosedAndLinkedToBox = 2,
    ClosedAndExported = 3,
}

export enum MediumType {
    PaperBased = 0,
    ElectronicWithoutPhysicalCopy = 1,
    ElectronicWithPhysicalCopy = 2,
}

export enum Confidentiality {
    NonConfidential = 0,
    Confidential = 1,
    HighlyConfidential = 2,
}

export enum DigitizationType {
    Daily = 2,
    Backlog = 4,
}

export interface FileStatusConfig {
    value: number
    label: string
    description: string
    className: string
    icon: string
    color: string
}
