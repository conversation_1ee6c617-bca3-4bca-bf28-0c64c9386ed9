import React, { useState } from 'react'
import { <PERSON>, Button } from '@/components/ui'
import Upload from '@/components/ui/Upload'
import { TbPaperclip } from 'react-icons/tb'
import { useUploadDocumentAttachments } from '@/hooks/documents'
import toast from '@/components/ui/toast'
import Notification from '@/components/ui/Notification'
import UploadMedia from '@/assets/svg/UploadMedia'

interface AttachmentsUploadProps {
    documentId: string
    onUploadSuccess?: () => void
}

export default function AttachmentsUpload({
    documentId,
    onUploadSuccess,
}: AttachmentsUploadProps) {
    const [uploadedFiles, setUploadedFiles] = useState<File[]>([])
    const [isUploading, setIsUploading] = useState(false)

    const uploadAttachmentsMutation = useUploadDocumentAttachments()

    const handleUpload = async () => {
        if (uploadedFiles.length === 0) return

        setIsUploading(true)
        try {
            await uploadAttachmentsMutation.mutateAsync({
                documentId,
                attachments: uploadedFiles,
            })
            setUploadedFiles([])
            onUploadSuccess?.()
            toast.push(
                <Notification
                    title={'Successfully uploaded attachments'}
                    type="success"
                />,
                { placement: 'top-center' },
            )
        } catch (error) {
            console.error('Upload failed:', error)
            toast.push(<Notification title={'Upload failed'} type="danger" />, {
                placement: 'top-center',
            })
        } finally {
            setIsUploading(false)
        }
    }

    return (
        <Card className="p-6">
            <div className="mb-4">
                <h3 className="text-lg font-semibold text-gray-900 mb-2 flex items-center">
                    <TbPaperclip className="w-5 h-5 mr-2" />
                    Upload Attachments
                </h3>
                <p className="text-sm text-gray-500">
                    Upload additional files related to this document
                </p>
            </div>

            <Upload
                draggable
                multiple
                className="mb-4 bg-gray-100 dark:bg-transparent"
                onChange={setUploadedFiles}
                onFileRemove={setUploadedFiles}
            >
                <div className="my-4 text-center">
                    <div className="text-6xl mb-4 flex justify-center">
                        <UploadMedia height={150} width={200} />
                    </div>
                    <p className="font-semibold">
                        <span className="text-gray-800 dark:text-white">
                            Drop your files here, or{' '}
                        </span>
                        <span className="text-blue-500">browse</span>
                    </p>
                    <p className="mt-1 font-semibold opacity-60 dark:text-white">
                        through your machine
                    </p>
                </div>
            </Upload>

            {uploadedFiles.length > 0 && (
                <div className="mt-4">
                    <Button
                        block
                        loading={
                            isUploading || uploadAttachmentsMutation.isPending
                        }
                        variant="solid"
                        disabled={uploadedFiles.length === 0}
                        onClick={handleUpload}
                    >
                        Upload {uploadedFiles.length} File
                        {uploadedFiles.length > 1 ? 's' : ''}
                    </Button>
                </div>
            )}
        </Card>
    )
}
