import React, { useState } from 'react'
import { <PERSON>, Button } from '@/components/ui'
import { TbUpload, TbX, Tb<PERSON><PERSON>ck, Tb<PERSON><PERSON>c<PERSON> } from 'react-icons/tb'
import { useUploadDocumentAttachments } from '@/hooks/documents'

interface AttachmentsUploadProps {
    documentId: string
    onUploadSuccess?: () => void
}

export default function AttachmentsUpload({
    documentId,
    onUploadSuccess,
}: AttachmentsUploadProps) {
    const [selectedFiles, setSelectedFiles] = useState<File[]>([])
    const [dragActive, setDragActive] = useState(false)

    const uploadAttachmentsMutation = useUploadDocumentAttachments()

    const handleDrag = (e: React.DragEvent) => {
        e.preventDefault()
        e.stopPropagation()
        if (e.type === 'dragenter' || e.type === 'dragover') {
            setDragActive(true)
        } else if (e.type === 'dragleave') {
            setDragActive(false)
        }
    }

    const handleDrop = (e: React.DragEvent) => {
        e.preventDefault()
        e.stopPropagation()
        setDragActive(false)

        const files = Array.from(e.dataTransfer.files)
        if (files.length > 0) {
            setSelectedFiles((prev) => [...prev, ...files])
        }
    }

    const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
        const files = Array.from(e.target.files || [])
        if (files.length > 0) {
            setSelectedFiles((prev) => [...prev, ...files])
        }
    }

    const handleUpload = async () => {
        if (selectedFiles.length === 0) return

        try {
            await uploadAttachmentsMutation.mutateAsync({
                documentId,
                attachments: selectedFiles,
            })
            setSelectedFiles([])
            onUploadSuccess?.()
        } catch (error) {
            console.error('Upload failed:', error)
        }
    }

    const removeFile = (index: number) => {
        setSelectedFiles((prev) => prev.filter((_, i) => i !== index))
    }

    const clearAllFiles = () => {
        setSelectedFiles([])
    }

    const formatFileSize = (bytes: number) => {
        if (bytes === 0) return '0 Bytes'
        const k = 1024
        const sizes = ['Bytes', 'KB', 'MB', 'GB']
        const i = Math.floor(Math.log(bytes) / Math.log(k))
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    }

    return (
        <Card className="p-6">
            <div className="mb-4">
                <h3 className="text-lg font-semibold text-gray-900 mb-2 flex items-center">
                    <TbPaperclip className="w-5 h-5 mr-2" />
                    Upload Attachments
                </h3>
                <p className="text-sm text-gray-500">
                    Upload additional files related to this document
                </p>
            </div>

            <div
                className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors mb-4 ${
                    dragActive
                        ? 'border-blue-400 bg-blue-50'
                        : 'border-gray-300 hover:border-gray-400'
                }`}
                onDragEnter={handleDrag}
                onDragLeave={handleDrag}
                onDragOver={handleDrag}
                onDrop={handleDrop}
            >
                <TbUpload className="mx-auto text-3xl text-gray-400 mb-3" />
                <p className="text-gray-600 mb-2">
                    Drag and drop files here, or
                </p>
                <label className="cursor-pointer">
                    <span className="text-blue-600 hover:text-blue-700 font-medium">
                        browse files
                    </span>
                    <input
                        multiple
                        type="file"
                        className="hidden"
                        onChange={handleFileSelect}
                    />
                </label>
                <p className="text-xs text-gray-500 mt-2">
                    Multiple files allowed
                </p>
            </div>

            {selectedFiles.length > 0 && (
                <div className="space-y-4">
                    <div className="flex items-center justify-between">
                        <h4 className="font-medium text-gray-900">
                            Selected Files ({selectedFiles.length})
                        </h4>
                        <Button
                            variant="default"
                            size="sm"
                            disabled={uploadAttachmentsMutation.isPending}
                            onClick={clearAllFiles}
                        >
                            Clear All
                        </Button>
                    </div>

                    <div className="space-y-2 max-h-60 overflow-y-auto">
                        {selectedFiles.map((file, index) => (
                            <div
                                key={`${file.name}-${index}`}
                                className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                            >
                                <div className="flex items-center space-x-3 flex-1 min-w-0">
                                    <div className="flex-1 min-w-0">
                                        <p className="font-medium text-gray-900 truncate">
                                            {file.name}
                                        </p>
                                        <p className="text-sm text-gray-500">
                                            {formatFileSize(file.size)}
                                        </p>
                                    </div>
                                </div>
                                <Button
                                    className="p-2 hover:bg-gray-200 rounded-full transition-colors flex-shrink-0"
                                    disabled={
                                        uploadAttachmentsMutation.isPending
                                    }
                                    onClick={() => removeFile(index)}
                                >
                                    <TbX className="w-4 h-4 text-gray-500" />
                                </Button>
                            </div>
                        ))}
                    </div>

                    <div className="flex gap-3">
                        <Button
                            loading={uploadAttachmentsMutation.isPending}
                            className="flex-1"
                            onClick={handleUpload}
                        >
                            <TbCheck className="w-4 h-4 mr-2" />
                            Upload {selectedFiles.length} File
                            {selectedFiles.length > 1 ? 's' : ''}
                        </Button>
                        <Button
                            variant="default"
                            disabled={uploadAttachmentsMutation.isPending}
                            onClick={clearAllFiles}
                        >
                            Cancel
                        </Button>
                    </div>
                </div>
            )}
        </Card>
    )
}
