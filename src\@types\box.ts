import { PaginationResponse } from './global'

export interface Box {
    boxId: string
    organizationalNodeName: string
    numberOfFiles: number
    closureStrapSerialNumber: number
    boxStatus: number
    createdAt: string
    createdByUsername: string
}

export interface BoxFile {
    fileId: string
    fileTitle: string
    numberOfDocuments: number
    numberOfPages: number
}

export interface BoxDetails {
    boxId: string
    organizationalNodeId: string
    organizationalNodeName: string
    numberOfFiles: number
    closureStrapId: number
    closureStrapSerialNumber: number
    boxStatus: number
    createdByID: string
    createdByUsername: string
    createdAt: string
    updatedByID: string
    updatedByUsername: string
    updatedAt: string
    files: BoxFile[]
}

export type BoxesResponse = {
    items: Box[]
} & PaginationResponse
